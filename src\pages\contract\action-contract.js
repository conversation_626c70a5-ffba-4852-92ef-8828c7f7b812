import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  <PERSON><PERSON>,
  Col,
  Container,
  Label,
  Modal,
  ModalB<PERSON>,
  ModalFooter,
  <PERSON>dalHeader,
  <PERSON>,
} from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState } from "react";
import toastr from "toastr";
import { useLocation, useNavigate } from "react-router-dom";
import { contractAPis } from "../../apis/contract/api";
import { clientsQueries } from "../../apis/clients/query";
import { useForm } from "react-hook-form";
import { ContracyQueries } from "../../apis/contract/query";
import { ContractQueries } from "../../apis/types/contract-type/query";
import { productQueries } from "../../apis/products/query";
import TableContainer from "../../components/Common/TableContainer";
import {
  formatDate,
  handleBackendErrors,
  validateDateTime,
} from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";
import ActionSection from "../../components/Common/ActionSection";
import CustomSelect from "../../components/Common/Select";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import { FormField } from "../bills/action-bill";
import CustomInput from "../../components/Common/Input";
import TextAreaField from "../../components/Common/textArea";
import Input from "../../components/Common/Input";
import { contractTemplateQueries } from "../../apis/contract_templete/query";
import { MdDeleteSweep } from "react-icons/md";
import { FaPenToSquare } from "react-icons/fa6";

const BillsActions = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  // Get type and template_id from URL with format type=1?template_id=5
  const typeParam = queryParams.get("type") || "";
  const typeValue = typeParam.split("?")[0];
  const [typeFromUrl, setTypeFromUrl] = useState(Number(typeValue));

  // Get template_id from the second part of type parameter
  const templateId = typeParam.split("?")[1]?.split("=")[1];

  // Handle both URL formats: id=29?Show=true and id=29&show=true
  const idParam = queryParams.get("id") || "";
  const selectId = Number(idParam.split("?")[0]);

  // Check for both formats: the old format with ?Show=true and the new format with &show=true
  let isShow = false;
  let isDuplicate = false;

  // Check if the URL contains the old format with ?Show=true
  if (idParam.includes("?Show") || idParam.includes("?show")) {
    isShow = true;
  }

  if (idParam.includes("?isDuplicate") || idParam.includes("?isDuplicate")) {
    isDuplicate = true;
  }

  // Check if the URL contains the new format with &show=true
  if (
    queryParams.get("show") === "true" ||
    queryParams.get("Show") === "true"
  ) {
    isShow = true;
  }

  if (
    queryParams.get("isDuplicate") === "true" ||
    queryParams.get("isDuplicate") === "true"
  ) {
    isDuplicate = true;
  }

  const [openAddModal, setOpenAddModal] = useState(false);
  const [openOtherProductAddModal, setOpenOtherProductAddModal] =
    useState(false);
  const [selectedOtherProductId, setSelectedOtherProductId] = useState(0);
  const [optionGroup, setOptionGroup] = useState([]); // Assuming you have a way to populate this
  const [productList, setProductList] = useState([]);
  const [otherProductList, setOtherProductList] = useState([]);
  const [selectedProductId, setSelectedProductId] = useState(0);
  const [disbleVisitNumber, setDisableVisitNumber] = useState(0);
  const { t } = useTranslation();
  const { pathname } = useLocation();
  const { data, isLoading } = ContracyQueries.useGetOneContract({
    id: Number(selectId),
  });

  const { data: contract_type } = ContractQueries.useGetAllContractTypes({});

  const [instanceSections, setInstanceSections] = useState([]);

  // Create dynamic schema based on instanceSections
  const schema = useMemo(() => {
    const baseSchema = {
      // contract_type_id: yup.number().required(t("common.field_required")),
      client_id: yup
        .object()
        .shape({
          label: yup.string().required(),
          value: yup.number().required(),
        })
        .required(t("common.field_required")),

      contract_start_date: yup
        .date()
        .typeError(t("contracts.invalid_date_format")) // في حال كانت القيمة ليست تاريخ
        .required(t("contracts.start_date_required"))
        .max(yup.ref("contract_end_date"), t("contracts.start_date_after_end")),

      contract_end_date: yup
        .date()
        .typeError(t("contracts.invalid_date_format"))
        .required(t("contracts.end_date_required"))
        .min(
          yup.ref("contract_start_date"),
          t("contracts.end_date_before_start")
        ),
      res_name: yup.string().required(t("common.field_required")),
      subscription_amount: yup
        .number()
        .positive(t("contracts.subscription_amount_positive"))
        .required(t("common.field_required"))
        .min(0, t("bills.validation.greater_than_0", { min: 0 }))
        .max(9999999, t("bills.validation.greater_than_10m", { max: 9999999 }))
        // .max(3, t("contracts.visit_number_max", { max: 3 }))
        .typeError(t("common.valid_number")),
      steal_amount:
        typeFromUrl === 2
          ? yup
              .number()
              .positive(t("contracts.subscription_amount_positive"))
              .required(t("common.field_required"))
              .min(0, t("bills.validation.greater_than_0", { min: 0 }))
              .max(
                9999999,
                t("bills.validation.greater_than_10m", { max: 9999999 })
              )
              // .max(3, t("contracts.visit_number_max", { max: 3 }))
              .typeError(t("common.valid_number"))
          : null,
      visit_number: yup
        .number()
        .min(0, t("contracts.visit_number_min", { min: 0 }))
        .max(3, t("contracts.visit_number_max", { max: 3 }))
        .required(t("contracts.visit_number_required"))
        .typeError(t("common.valid_number")),
    };

    // Add dynamic validation for section terms
    if (instanceSections && instanceSections.length > 0) {
      instanceSections.forEach((section, sectionIndex) => {
        section.terms.forEach((term, termIndex) => {
          const fieldName = `section_${sectionIndex}_term_${termIndex}`;
          baseSchema[fieldName] = yup
            .string()
            .required(t("common.field_required"));
        });
      });
    }

    return yup.object(baseSchema).required();
  }, [instanceSections, typeFromUrl, t]);

  const today = new Date(); // Get the current date
  const endDate = new Date(); // Clone the current date
  endDate.setDate(today.getDate() + 365); // Add 365 days to the current date

  const initialSTate = {
    contract_type_id: null,
    client_id: null,
    contract_start_date: formatDate(today),
    contract_end_date: formatDate(endDate),
    notes: "",
    res_name: "",
    visit_number: null,
    subscription_amount: null,
    quant: null,
    product_id: null,
    product_note: "",
    other_product_name: "",
    other_product_quant: null,
    steal_amount: 0,
  };

  const selectedContractType = contract_type?.result?.find(
    (item) => item.type === typeFromUrl
  );

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    setError,
    control,
    watch,
    trigger,
    clearErrors,
    setValue,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  const { data: clients } = clientsQueries.useGetAll({ status: 1 });

  const { data: products } = productQueries.useGetAll({
    status: 1,
    contractType: selectedContractType?.id,
  });

  console.log("errors", errors);

  const breadcrumbItems = [
    { title: t("contracts.contracts"), link: "/contract" },
    {
      title: isShow
        ? t("common.show")
        : selectId
        ? t("common.update")
        : t("common.create"),
      link: pathname,
    },
  ];
  const navigate = useNavigate();

  const handelCancel = () => {
    navigate(-1);
    reset();
  };

  const handelCloseAddModal = () => {
    setOpenAddModal(false);
    setOpenOtherProductAddModal(false);
    resetInputs();
  };

  const resetInputs = () => {
    setValue("product_id", null);
    setValue("quant", 0);
    setValue("product_note", "");
    setValue("other_product_name", "");
    setValue("other_product_quant", 0);
    setValue("other_product_note", "");
    setSelectedProductId(0);
    setSelectedOtherProductId(0);
  };

  const resetProductInputs = () => {
    setValue("product_id", null);
    setValue("quant", 0);
    setValue("product_note", "");
    setSelectedProductId(0);
  };

  const resetProductInputsWithoutId = () => {
    setValue("product_id", null);
    setValue("quant", 0);
    setValue("product_note", "");
  };

  const resetOtherProductInputs = () => {
    setValue("other_product_name", "");
    setValue("other_product_quant", 0);
    setValue("other_product_note", "");
    setSelectedOtherProductId(0);
  };

  const handelOpenAddModal = () => {
    resetProductInputs(); // This will reset selectedProductId to 0 for new products
    setOpenAddModal(true);
  };

  const handelOpenOtherProductAddModal = () => {
    resetOtherProductInputs();
    setOpenOtherProductAddModal(true);
  };

  useEffect(() => {
    if (watch("product_id")?.value) {
      const product = products.result.find(
        (item) => watch("product_id")?.value
      );
      if (Number(watch("quant")) > 0 && openAddModal) {
        // Clear previous errors
        clearErrors("quant");
        if (Number(watch("quant")) <= 0) {
          setError("quant", {
            type: "manual",
            message: t("bills.validation.greater_than_0"),
          });
        }

        // Check if value is within the specified range
        if (Number(watch("quant")) < product.min_quant) {
          setError("quant", {
            type: "manual",
            message: t("bills.validation.min_value", {
              value: product.min_quant,
            }),
          });
        } else if (Number(watch("quant")) > product.max_quant) {
          setError("quant", {
            type: "manual",
            // message: `The value must be less than or equal to ${product.max_quant}`,
            message: t("bills.validation.max_value", {
              value: product.max_value,
            }),
          });
        }
      }
    }
  }, [watch("quant"), watch("product_id"), openAddModal]);

  const fieldsNames = [
    {
      name: "visit_number",
      isRequired: false,
      errorMessage: errors?.visit_number?.message,
      label: t("common.visit_number"),
      type: "number",
      showIn: true,
      cols: 2,
      disable: selectId,
    },
    {
      name: "subscription_amount",
      isRequired: false,
      errorMessage: errors?.subscription_amount?.message,
      label: t("contracts.subscription_amount"),
      type: "number",
      showIn: true,
      cols: 2,
    },
    {
      name: "steal_amount",
      isRequired: false,
      errorMessage: errors?.steal_amount?.message,
      label: t("contracts.steal_amount"),
      type: "number",
      showIn: typeFromUrl === 2,
      cols: 2,
    },
    {
      name: "res_name",
      isRequired: false,
      errorMessage: errors?.res_name?.message,
      label: t("bills.response_name"),
      type: "text",
      showIn: true,
      cols: 2,
    },
  ];

  const dateFields = [
    {
      name: "contract_start_date",
      isRequired: false,
      errorMessage: errors?.contract_start_date?.message,
      label: t("contracts.contract_start_date"),
      type: "date",
      showIn: true,
    },
    {
      name: "contract_end_date",
      isRequired: false,
      errorMessage: errors?.contract_end_date?.message,
      label: t("contracts.contract_end_date"),
      type: "date",
      showIn: true,
    },
  ];

  const textAreaField = [
    {
      id: 0,
      name: "notes",
      label: t("common.note"),
      isRequired: false,
      showIn: true,
    },
  ];

  useEffect(() => {
    if (products?.result?.length > 0) {
      setOptionGroup(
        products.result.map((item) => ({
          label: item.name,
          value: item.id,
        }))
      );
    }
  }, [products?.result]);

  const productsFields = [
    {
      id: 0,
      field: (
        <Col xs={12} className="mb-4">
          {/* <div>
            <Label className="form-label" htmlFor="total">
              {t("common.select")} {t("common.product")}
            </Label>
          </div>
          <Controller
            name="product_id"
            control={control}
            defaultValue={[]}
            render={({ field }) => (
              <Select
                isDisabled={isShow}
                {...field}
                isClearable
                 options={optionGroup.includes(productList)}
                options={optionGroup.filter(
                  (option) =>
                    !productList.some(
                      (product) => product.product_id === option.value
                    )
                )}
                isMulti={false}
                styles={{
                  menuList: (props) => ({
                    ...props,
                    paddingBottom: 10,
                    height: "100px",
                  }),
                  menu: (props) => ({
                    ...props,
                    height: "100px",
                  }),
                }}
                classNamePrefix="select2-selection"
              />
            )}
          /> */}
          <CustomSelect
            control={control}
            error={errors.product_id}
            label={t("common.product")}
            // options={clientOptions}
            options={optionGroup.filter(
              (option) =>
                !productList.some(
                  (product) => product.product_id === option.value
                )
            )}
            name="product_id"
            isDisabled={isShow}
          />
        </Col>
      ),
      showIn: true,
    },
    {
      id: 1,
      field: (
        <Col>
          {/* <div className="mb-4">
            <Label className="form-label" htmlFor="quant">
              {t("common.quant")}
            </Label>
            <input
              name=""
              {...register(`quant`, {
                // required: true,
              })}
              disabled={!watch("product_id") || isShow}
              placeholder="...."
              type="number"
              className={`form-control ${errors?.quant ? `is-invalid` : ``}`}
            />
            {errors?.quant && (
              <div className="invalid-feedback">{errors?.quant?.message}</div>
            )}
          </div> */}
          <Input
            control={control}
            name="quant"
            isDisabled={!watch("product_id") || isShow}
            placeholder={t("common.quant")}
            type="number"
            min={1}
            max={5000}
            error={errors?.quant}
          />
        </Col>
      ),
      showIn: true,
    },
  ];

  const handelAddProductToList = () => {
    if (!watch("quant")) {
      // Set an error if notes are empty
      setError("quant", {
        type: "manual",
        message: t("contracts.min_quant"),
      });
      return; // Do not proceed to API call
    }
    if (Number(watch("quant")) < 0) {
      // Set an error if notes are empty
      setError("quant", {
        type: "manual",
        message: t("bills.validation.greater_than_0"),
      });
      return; // Do not proceed to API call
    }
    if (Number(watch("quant")) > 5000) {
      // Set an error if notes are empty
      setError("quant", {
        type: "manual",
        message: t("bills.validation.greater_than_5000"),
      });
      return; // Do not proceed to API call
    }

    if (errors.quant) {
      return;
    }
    try {
      const selectedProduct = watch("product_id");
      const productQuant = Number(watch("quant"));
      const productNote = watch("product_note");

      console.log("handelAddProductToList called:", {
        selectedProductId,
        selectedProduct,
        productQuant,
        productNote,
        isUpdate: selectedProductId > 0,
      });

      if (selectedProduct && productQuant > 0) {
        setProductList((prev) => {
          console.log("setProductList called with prev:", prev);
          console.log("selectedProductId:", selectedProductId);

          // If updating (selectedProductId is set)
          if (selectedProductId > 0) {
            console.log(
              "Updating existing product with ID:",
              selectedProductId
            );

            // Check if the new selected product already exists in the list (excluding the current one being updated)
            const existingProduct = prev.find(
              (product) =>
                product.product_id === selectedProduct.value &&
                product.product_id !== selectedProductId
            );

            if (existingProduct) {
              // If the new product already exists, remove the old one and update the existing one
              const result = prev
                .filter((product) => product.product_id !== selectedProductId)
                .map((product) =>
                  product.product_id === selectedProduct.value
                    ? {
                        ...product,
                        quant: product.quant + productQuant, // Add quantities
                        notes: productNote || product.notes,
                      }
                    : product
                );
              console.log("Merged result:", result);
              return result;
            } else {
              // Normal update - just update the existing product
              const result = prev.map((product) =>
                product.product_id === selectedProductId
                  ? {
                      ...product,
                      product_id: selectedProduct.value,
                      product_name: selectedProduct.label,
                      quant: productQuant,
                      notes: productNote,
                    }
                  : product
              );
              return result;
            }
          }

          // Adding new product logic
          // Check if the product with the same product_id exists
          const existingProductIndex = prev.findIndex(
            (product) => product.product_id === selectedProduct.value
          );

          if (existingProductIndex !== -1) {
            // If it exists, update the existing product
            const updatedList = [...prev];
            updatedList[existingProductIndex] = {
              ...updatedList[existingProductIndex],
              quant: productQuant, // Update quantity
              notes: productNote || updatedList[existingProductIndex].notes, // Update note if provided
            };
            return updatedList;
          } else {
            // If it doesn't exist, add it to the list
            return [
              ...prev,
              {
                product_id: selectedProduct?.value,
                product_name: selectedProduct?.label,
                quant: productQuant,
                notes: productNote,
              },
            ];
          }
        });

        console.log("Product list updated, resetting inputs and closing modal");
        resetProductInputsWithoutId();
        setOpenAddModal(false);
        // Reset selectedProductId after the modal is closed
        setTimeout(() => {
          setSelectedProductId(0);
        }, 100);
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  const handelAddOtherProductToList = () => {
    const productName = watch("other_product_name");
    const productQuant = Number(watch("other_product_quant"));
    const productNote = watch("other_product_note");

    if (!productName?.trim()) {
      setError("other_product_name", {
        type: "manual",
        message: t("contracts.product_name_required"),
      });
      return;
    }

    if (!productQuant || productQuant <= 0) {
      setError("other_product_quant", {
        type: "manual",
        message: t("contracts.min_quant"),
      });
      return;
    }

    if (productQuant > 5000) {
      setError("other_product_quant", {
        type: "manual",
        message: t("bills.validation.greater_than_5000"),
      });
      return;
    }

    try {
      setOtherProductList((prev) => {
        // Check if we're updating an existing product
        if (selectedOtherProductId) {
          return prev.map((item) =>
            item.id === selectedOtherProductId
              ? {
                  ...item,
                  product_name: productName.trim(),
                  quant: productQuant,
                  notes: productNote,
                }
              : item
          );
        }

        // Otherwise handle as a new product
        const existingProductIndex = prev.findIndex(
          (product) =>
            product.product_name.toLowerCase() === productName.toLowerCase()
        );

        if (existingProductIndex !== -1) {
          const updatedList = [...prev];
          updatedList[existingProductIndex] = {
            ...updatedList[existingProductIndex],
            quant: productQuant,
            notes: productNote || updatedList[existingProductIndex].notes,
          };
          return updatedList;
        }

        return [
          ...prev,
          {
            product_name: productName.trim(),
            quant: productQuant,
            notes: productNote || "",
            id: performance.now(),
          },
        ];
      });

      resetOtherProductInputs();
      setOpenOtherProductAddModal(false);
      // toastr.success(t("contracts.other_product_added"));
    } catch (error) {
      console.error("Error adding other product:", error);
      toastr.error(t("common.error_occurred"));
    }
  };

  const handelSetUpdate = (id) => {
    console.log("handelSetUpdate called with id:", id);

    // Find the current product first
    const currentProduct = productList.find((item) => item.product_id === id);

    if (!currentProduct) {
      console.error("Product not found for update:", id);
      return;
    }

    // Set the selected product ID
    setSelectedProductId(id);

    // Set form values
    setValue("product_id", {
      value: currentProduct.product_id,
      label: currentProduct.product_name,
    });
    setValue("quant", currentProduct.quant);
    setValue("product_note", currentProduct.notes);

    // Open modal after setting values
    setOpenAddModal(true);
  };

  const handelUpdate = ({ id }) => {
    try {
      // First set the selected product ID
      setSelectedOtherProductId(id);

      // Find the product
      const productSelected = otherProductList.find((item) => item.id === id);

      if (!productSelected) {
        toastr.error(t("contracts.product_not_found"));
        return;
      }

      // Set form values
      setValue("other_product_name", productSelected.product_name);
      setValue("other_product_quant", productSelected.quant);
      setValue("other_product_note", productSelected.notes || "");

      // Open modal after setting values
      setOpenOtherProductAddModal(true);
    } catch (error) {
      console.error("Error updating product:", error);
      toastr.error(t("common.error_occurred"));
    }
  };

  const deleteOtherProduct = ({ id }) => {
    setOtherProductList((prev) => prev.filter((item) => item.id !== id));
  };

  const { data: template, isLoading: isLoadingTemplates } =
    contractTemplateQueries.useGet({
      id: Number(templateId),
      enabled: !!templateId,
    });

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      const returnedProduct = [];
      data?.result.details.map((item) => {
        returnedProduct.push({
          product_id: item.product.id,
          product_name: item.product.name,
          quant: item.quant,
          notes: item.notes,
          price: 1,
        });
      });
      // Parse the array of JSON strings into an array of objects
      const parsedArray =
        data?.result?.other_devices &&
        JSON.parse(data?.result?.other_devices).map((item) =>
          typeof item === "string" ? JSON.parse(item) : item
        );

      const otherReturnedProduct = [];
      parsedArray?.map((item) => {
        otherReturnedProduct.push({
          product_name: item.product_name,
          quant: item.quant,
          notes: item.notes,
        });
      });

      console.log("parsedArray", otherReturnedProduct);
      setProductList(returnedProduct);
      setOtherProductList(otherReturnedProduct);

      // Handle sections and terms
      if (data?.result?.instance_sections) {
        const sections = data.result.instance_sections
          .filter((section) =>
            section.terms.some((term) => term.is_editable === 1)
          )
          .map((section) => ({
            title: section.title,
            order: section.order,
            is_active: section.is_active,
            is_editable: section.is_editable,
            terms: section.terms
              .filter((term) => term.is_editable === 1)
              .map((term) => ({
                text: term.text,
                order: term.order,
                is_active: term.is_active,
                is_editable: term.is_editable,
              })),
          }));

        setInstanceSections(sections);
      }

      // Populate form with role data when loaded
      reset({
        client_id: {
          label: `${data?.result.client.company_name}/${data?.result.client.full_name}`,
          value: data?.result.client.id,
        },
        contract_end_date: data?.result.contract_end_date,
        res_name: data?.result.res_name,
        contract_start_date: data?.result.contract_start_date?.split("T")[0],
        visit_number: data?.result.client.visit_number,
        steal_amount: data?.result.steal_amount,
        notes: data?.result.notes,
        subscription_amount: data?.result.subscription_amount,
      });
      setTypeFromUrl(data?.result.contract_type.id);
    }
  }, [selectId, isLoading, data]);

  useEffect(() => {
    if (template?.result && !isLoadingTemplates && !selectId) {
      // Initialize instance sections from template only in create mode
      const sections = template.result.sections
        .filter((section) =>
          section.terms.some((term) => term.is_editable === 1)
        )
        .map((section) => ({
          title: section.title,
          order: section.order,
          is_active: section.is_active,
          is_editable: section.is_editable,
          terms: section.terms
            .filter((term) => term.is_editable === 1)
            .map((term) => ({
              text: term.text,
              order: term.order,
              is_active: term.is_active,
              is_editable: term.is_editable,
            })),
        }));
      setInstanceSections(sections);
    }
  }, [template, isLoadingTemplates, selectId]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const handleTermChange = (sectionIndex, termIndex, newText) => {
    setInstanceSections((prev) => {
      const newSections = [...prev];
      newSections[sectionIndex].terms[termIndex].text = newText;
      return newSections;
    });

    // Update form value for validation
    const fieldName = `section_${sectionIndex}_term_${termIndex}`;
    setValue(fieldName, newText);
  };

  // Set initial values for dynamic fields when instanceSections change
  useEffect(() => {
    if (instanceSections && instanceSections.length > 0) {
      instanceSections.forEach((section, sectionIndex) => {
        section.terms.forEach((term, termIndex) => {
          const fieldName = `section_${sectionIndex}_term_${termIndex}`;
          setValue(fieldName, term.text || "");
        });
      });
    }
  }, [instanceSections, setValue]);

  const renderTemplateSections = () => {
    // if (!template?.result?.sections||instanceSections) return null;

    return (
      <div className="mt-4">
        <h4 style={{ fontSize: 18 }}>{t("common.sections_with_terms")}</h4>
        {instanceSections.map((section, sectionIndex) => (
          <div key={section.id} className="mb-4">
            <h5 style={{ fontSize: 15 }}>
              {sectionIndex + 1} - {section.title}
            </h5>
            {section.terms.map((term, termIndex) => {
              const fieldName = `section_${sectionIndex}_term_${termIndex}`;
              return (
                <div key={term.id} className="mb-3">
                  <TextAreaField
                    name={fieldName}
                    register={(name) =>
                      register(name, { required: t("common.field_required") })
                    }
                    placeholder={t("common.terms_of_section")}
                    defaultValue={term.text}
                    className="mb-2"
                    disabled={isShow}
                    rows={2}
                    error={errors[fieldName]}
                    onChange={(e) =>
                      handleTermChange(sectionIndex, termIndex, e.target.value)
                    }
                  />
                </div>
              );
            })}
          </div>
        ))}
      </div>
    );
  };

  const addFun = async (data) => {
    if (otherProductList.length === 0 && productList.length === 0) {
      toastr.error(t("contracts.product_validation"));
      return;
    }
    try {
      const now = new Date(Date.now());
      const formattedTime = now.toLocaleTimeString("en-GB", {
        hour12: false,
      });

      const dataToSend = {
        products: productList,
        status: 1,
        other_devices:
          otherProductList.length > 0 ? JSON.stringify(otherProductList) : null,
        ...data,
        client_id: data.client_id.value,
        contract_type_id: typeFromUrl,
        // contract_end_date: `${data.contract_end_date} ${formattedTime}`,
        // contract_start_date: `${data.contract_start_date} ${formattedTime}`,
        contract_end_date: formatDate(data.contract_end_date),
        contract_start_date: formatDate(data.contract_start_date),
        steal_amount: Number(data.steal_amount),
        subscription_amount: Number(data.subscription_amount),
        instance_sections: instanceSections, // Add instance sections to the request
      };

      const response = await contractAPis.add({ payload: dataToSend });
      toastr.success(response.message);
      navigate(-1);
      reset();
      setProductList([]);
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.error("Error:", error);
    }
  };

  const UpdateFun = async (data) => {
    if (otherProductList.length === 0 && productList.length === 0) {
      toastr.error(t("contracts.product_validation"));
      return;
    }
    try {
      const now = new Date(Date.now());
      const formattedTime = now.toLocaleTimeString("en-GB", {
        hour12: false,
      });

      const dataToSend = {
        products: productList,
        status: 1,
        other_devices:
          otherProductList.length > 0 ? JSON.stringify(otherProductList) : null,
        ...data,
        contract_end_date: formatDate(data.contract_end_date),
        contract_start_date: formatDate(data.contract_start_date),
        client_id: data.client_id.value,
        contract_type_id: typeFromUrl,
        steal_amount: Number(data.steal_amount),
        subscription_amount: Number(data.subscription_amount),
        instance_sections: instanceSections, // Add instance sections to the request
      };
      const response = await contractAPis.update({
        payload: dataToSend,
        id: selectId,
      });
      toastr.success(response.message);
      navigate(-1);
      reset();
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
  };

  const columns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.product"),
      accessor: "product_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.quant"),
      accessor: "quant",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.note"),
      accessor: "notes",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          !isShow && (
            <div className="d-flex align-items-center gap-2 justify-content-start">
              <div
                className="text-primary"
                onClick={() => {
                  handelSetUpdate(cellProps.product_id);
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-16"></i> */}
                <FaPenToSquare size={14} className="mx-2" />
              </div>
              <div
                onClick={() => {
                  handelFilterFromProductList(cellProps.product_id);
                }}
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                <MdDeleteSweep size={18} className="mx-2" />
              </div>
            </div>
          )
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ];
  const rowData = useMemo(
    () =>
      productList.length > 0
        ? productList
            .map((item, index) => ({
              product_id: item.product_id,
              id_toShow: index + 1,
              product_name: item.product_name,
              quant: item.quant,
              notes: item.notes,
            }))
            .reverse()
        : [],
    [productList]
  );

  const otherRowData = useMemo(
    () =>
      otherProductList.length > 0
        ? otherProductList
            .map((item, index) => ({
              id: item.id,
              id_toShow: index + 1,
              product_name: item.product_name,
              other_product_quant: item.quant,
              notes: item.notes || "",
            }))
            .reverse()
        : [],
    [otherProductList]
  );

  const otherColumns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.product"),
      accessor: "product_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.quant"),
      accessor: "other_product_quant",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.note"),
      accessor: "notes",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          !isShow && (
            <div className="d-flex align-items-center gap-2 justify-content-start">
              <div
                className="text-primary"
                onClick={() => {
                  handelUpdate({ id: cellProps.id });
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-16"></i> */}
                <FaPenToSquare size={14} className="mx-2" />
              </div>
              <div
                onClick={() => {
                  deleteOtherProduct({ id: cellProps.id });
                }}
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                <MdDeleteSweep size={18} className="mx-2" />
              </div>
            </div>
          )
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ];

  const handelFilterFromProductList = (id) => {
    // Filter out the item with the given id from both productList and rowData
    const updatedProductList = productList.filter(
      (item) => item.product_id !== id
    );

    // Update the productList state (this will automatically trigger a re-render)
    setProductList(updatedProductList);
  };

  const clientOptions = useSetSelectOptions({
    data: clients?.result,
    getOption: (item) => ({
      label: `${item.company_name}/${item.full_name || "---"}`,
      value: item.id,
    }),
  });

  const contractType = [
    t("types.contract_types.sell_contract"),
    t("types.contract_types.rent_contract"),
  ];

  useEffect(() => {
    if (watch("client_id")?.value) {
      const selectedClient = clients?.result.find(
        (item) => item.id === watch("client_id")?.value
      );
      if (selectedClient?.visit_number) {
        setDisableVisitNumber(true);
      } else {
        setDisableVisitNumber(false);
      }
      setValue("visit_number", selectedClient?.visit_number);
    }
  }, [watch("client_id")?.value]);

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("contracts.contracts")}
          breadcrumbItems={breadcrumbItems}
          isPagination={true}
          canPermission={"contract.store"}
          titleOfPage={
            isDuplicate
              ? t("common.duplicate")
              : isShow
              ? t("common.show")
              : selectId
              ? t("common.update")
              : t("common.create")
          }
          titleOfSection={t("contracts.contracts")}
          currentPageLink={``}
          titleLink={`/contract`}
        />

        <ActionSection
          isLoading={isLoading}
          handleSubmit={handleSubmit}
          selectId={selectId}
          UpdateFun={isDuplicate ? addFun : UpdateFun}
          addFun={addFun}
          handelCancel={handelCancel}
          isShow={isShow}
          customText={isDuplicate && t("common.save")}
          isSubmitting={isSubmitting}
        >
          <h1 style={{ fontSize: 18, paddingBottom: 2 }}>
            {t("contracts.contract_type")}: {contractType[typeFromUrl - 1]}
          </h1>
          <Row className="g-3">
            <Col xs={3}>
              <CustomSelect
                control={control}
                error={errors.client_id}
                label={t("common.client")}
                options={clientOptions}
                name="client_id"
                isDisabled={isShow || selectId}
              />
            </Col>

            {fieldsNames.map(
              (field) =>
                field.showIn && (
                  <FormField
                    key={field.id}
                    field={field}
                    register={register}
                    errors={errors}
                    isShow={isShow}
                    t={t}
                    control={control}
                    placeholder={field.label}
                    cols={field.cols}
                    isDisabled={field.disable}
                  />
                )
            )}

            {dateFields.map((field) => (
              <FormField
                key={field.id}
                field={field}
                register={register}
                errors={errors}
                isShow={isShow}
                t={t}
                control={control}
                placeholder={field.label}
              />
            ))}

            {textAreaField.map((field) => (
              <Col key={field.id} xs={4}>
                <TextAreaField
                  name={field.name}
                  register={register}
                  placeholder={t("common.note")}
                  defaultValue=""
                  className="mb-2"
                  disabled={isShow}
                  error={errors?.notes}
                  rows={1}
                />
              </Col>
            ))}

            <Row>
              <Col
                className="mt-2"
                xs={selectedContractType?.type === 2 ? 12 : 6}
              >
                <TableContainer
                  hideSHowGFilter={true}
                  columns={columns || []}
                  data={rowData}
                  isSmall
                  hidePagination
                  customComponent={
                    <Button
                      type="button"
                      onClick={handelOpenAddModal}
                      color="primary"
                      className="btn-sm"
                      disabled={isShow ? true : false}
                    >
                      {t("common.add") + " " + t("products.products")}
                    </Button>
                  }
                />
              </Col>
              <Col className="mt-2" xs={6}>
                {selectedContractType?.type !== 2 && (
                  <TableContainer
                    hideSHowGFilter={true}
                    columns={otherColumns || []}
                    data={otherRowData}
                    isSmall
                    hidePagination
                    customComponent={
                      <Button
                        type="button"
                        onClick={handelOpenOtherProductAddModal}
                        color="primary"
                        className="btn-sm"
                        disabled={isShow ? true : false}
                      >
                        {t("common.add") +
                          " " +
                          t("contracts.other_product_name")}
                      </Button>
                    }
                  />
                )}
              </Col>

              {/* Add template sections */}
              {(template?.result || instanceSections?.length > 0) &&
                renderTemplateSections()}
            </Row>
          </Row>
        </ActionSection>
      </Container>
      <Modal isOpen={openAddModal} backdrop="static">
        <ModalHeader toggle={handelCloseAddModal}>
          {selectedProductId
            ? t("common.update") + " " + t("common.product")
            : t("common.add") + " " + t("common.product")}
        </ModalHeader>
        <ModalBody>
          <Row>
            {productsFields.map((item) => item.showIn && item.field)}

            <Col xs={12}>
              <div className="my-2">
                <Label className="form-label" htmlFor="product_note">
                  {t("common.note")}
                </Label>
                <textarea
                  id="product_note"
                  className={`form-control ${
                    errors?.product_note ? "is-invalid" : ""
                  }`}
                  placeholder={t("common.note")}
                  rows={4}
                  disabled={isShow}
                  {...register("product_note")}
                />
                {errors?.product_note && (
                  <div className="invalid-feedback">
                    {errors.product_note.message}
                  </div>
                )}
              </div>
            </Col>
          </Row>
          <ModalFooter>
            <Button
              type="button"
              className="btn-sm"
              color="light"
              onClick={handelCloseAddModal}
            >
              {t("common.close")}
            </Button>
            <Button
              type="button"
              onClick={handelAddProductToList}
              color="primary"
              className="btn-sm"
              disabled={!watch("product_id") && !watch("quant")}
            >
              {selectedProductId > 0 ? t("common.update") : t("common.add")}
            </Button>
          </ModalFooter>
        </ModalBody>
      </Modal>
      <Modal isOpen={openOtherProductAddModal} backdrop="static">
        <ModalHeader toggle={handelCloseAddModal}>
          {selectedOtherProductId > 0
            ? t("common.update") + " " + t("contracts.other_product")
            : t("contracts.add_other_products")}
        </ModalHeader>
        <ModalBody>
          <Row>
            <Col xs={6}>
              <CustomInput
                control={control}
                name="other_product_name"
                disabled={isShow}
                placeholder={t("products.product_name")}
                type="text"
                error={errors?.other_product_name}
              />
            </Col>
            <Col xs={6}>
              <CustomInput
                control={control}
                name="other_product_quant"
                disabled={isShow}
                placeholder={t("common.quant")}
                type="number"
                error={errors?.other_product_quant}
              />
            </Col>
          </Row>
          <Row>
            <Col xs={12}>
              <div className="mb-2">
                <Label className="form-label" htmlFor="other_product_note">
                  {t("common.note")}
                </Label>
                <textarea
                  id="other_product_note"
                  className={`form-control ${
                    errors?.other_product_note ? "is-invalid" : ""
                  }`}
                  placeholder={t("common.note")}
                  rows={4}
                  disabled={isShow}
                  {...register("other_product_note")}
                />
                {errors?.other_product_note && (
                  <div className="invalid-feedback">
                    {errors.other_product_note.message}
                  </div>
                )}
              </div>
            </Col>
          </Row>
          <ModalFooter>
            <Button
              type="button"
              color="light"
              className="btn-sm"
              onClick={handelCloseAddModal}
            >
              {t("common.close")}
            </Button>
            <Button
              type="button"
              onClick={handelAddOtherProductToList}
              color="primary"
              className="btn-sm"
              disabled={
                !watch("other_product_name") && !watch("other_product_quant")
              }
            >
              {selectedOtherProductId > 0
                ? t("common.update")
                : t("common.add")}
            </Button>
          </ModalFooter>
        </ModalBody>
      </Modal>
    </div>
  );
};
export default BillsActions;
